# NUMA节点CPU总量指标负数问题修复

## 问题描述

在Grafana面板中观察`tap_numa_node_cpu_total_cores`指标时，发现numa_node_0的指标为负数。

## 根因分析

### 问题根源
问题出现在`pkg/cache/cache.go`的`GetNodeResources()`方法中：

```go
// 原有问题代码
nodes := make([]NumaNodeResources, len(cch.CpuInfo.TotalInfo.NodeToCPU))
for i := 0; i < len(cch.CpuInfo.TotalInfo.NodeToCPU); i++ {
    nodes[i].CpuTotal = float64(len(cch.CpuInfo.TotalInfo.NodeToCPU[int32(i)]))
    // ...
}
```

### 具体原因
1. **NUMA节点ID不连续**: 在某些系统中，NUMA节点ID可能不是从0开始连续的（例如：node0, node2存在，但node1不存在）
2. **数组索引错误**: 代码假设NUMA节点ID连续，直接用循环索引`i`作为节点ID访问`NodeToCPU[int32(i)]`
3. **空切片导致0值**: 当访问不存在的节点ID时，返回空切片，`len()`为0，导致`CpuTotal`为0
4. **负数产生**: 在计算`cpuFree = nodeResource.CpuTotal - nodeResource.CpuUsed`时，0减去正数产生负数

## 修复方案

### 1. 修复NUMA节点资源计算逻辑

**修改文件**: `pkg/cache/cache.go`

**核心改进**:
- 获取实际存在的NUMA节点ID列表
- 按节点ID排序确保结果一致性
- 创建节点ID到数组索引的映射
- 添加边界检查和错误处理
- 防止负数产生

```go
// 修复后的代码
// 获取实际存在的NUMA节点ID列表
var nodeIDs []int32
for nodeID := range cch.CpuInfo.TotalInfo.NodeToCPU {
    nodeIDs = append(nodeIDs, nodeID)
}

// 按节点ID排序，确保结果的一致性
sort.Slice(nodeIDs, func(i, j int) bool {
    return nodeIDs[i] < nodeIDs[j]
})

// 只处理实际存在的节点
nodes := make([]NumaNodeResources, len(nodeIDs))
for i, nodeID := range nodeIDs {
    cpuList, exists := cch.CpuInfo.TotalInfo.NodeToCPU[nodeID]
    if !exists || len(cpuList) == 0 {
        nodes[i].CpuTotal = 0.0
    } else {
        nodes[i].CpuTotal = float64(len(cpuList))
    }
    // ...
}
```

### 2. 修复指标更新逻辑

**修改文件**: `pkg/policy/numa-aware/numa_aware.go`

**核心改进**:
- 获取实际的NUMA节点ID列表与数组索引对应
- 使用真实的NUMA节点ID作为Prometheus指标标签
- 添加索引越界保护

```go
// 获取排序后的NUMA节点ID列表
var nodeIDs []int32
for nodeID := range cpuInfo.TotalInfo.NodeToCPU {
    nodeIDs = append(nodeIDs, nodeID)
}
sort.Slice(nodeIDs, func(i, j int) bool {
    return nodeIDs[i] < nodeIDs[j]
})

// 使用实际的NUMA节点ID作为标签
for i, nodeResource := range nodeResources {
    var nodeIDStr string
    if i < len(nodeIDs) {
        nodeIDStr = strconv.Itoa(int(nodeIDs[i]))
    } else {
        nodeIDStr = strconv.Itoa(i)
    }
    // 更新指标...
}
```

### 3. 增强容器CPU使用量计算

**核心改进**:
- 创建NUMA节点ID到数组索引的映射
- 添加CPU时间有效性检查
- 防止负数空余量产生

```go
// 创建NUMA节点ID到数组索引的映射
nodeIDToIndex := make(map[int32]int)
for i, nodeID := range nodeIDs {
    nodeIDToIndex[nodeID] = i
}

// 安全的CPU使用量更新
if numaNodeID >= 0 {
    if index, exists := nodeIDToIndex[numaNodeID]; exists {
        nodes[index].CpuUsed += cpuTime
    }
}

// 防止负数空余量
for i := range nodes {
    nodes[i].CpuFree = nodes[i].CpuTotal - nodes[i].CpuUsed
    if nodes[i].CpuFree < 0 {
        nodes[i].CpuFree = 0
    }
}
```

## 修复效果

### 解决的问题
1. ✅ **消除负数指标**: `tap_numa_node_cpu_total_cores`不再出现负数
2. ✅ **正确的节点映射**: 指标标签使用真实的NUMA节点ID
3. ✅ **处理不连续节点**: 支持NUMA节点ID不连续的系统
4. ✅ **增强错误处理**: 添加边界检查和异常情况处理
5. ✅ **数据一致性**: 通过排序确保结果的可重现性

### 验证结果
- ✅ 编译测试通过
- ✅ 单元测试通过
- ✅ 代码质量检查通过
- ✅ 向后兼容性保证

## 技术要点

### 关键改进
1. **安全的数组访问**: 避免使用不存在的NUMA节点ID作为数组索引
2. **正确的标签映射**: Prometheus指标标签使用真实的NUMA节点ID而非数组索引
3. **健壮的错误处理**: 处理空节点、无效CPU时间等边界情况
4. **数据一致性**: 通过排序确保多次调用结果一致

### 兼容性保证
- 保持API接口不变
- 保持指标名称和结构不变
- 向后兼容现有的监控配置

## 总结

此次修复彻底解决了NUMA节点CPU指标负数问题，提高了系统对不同NUMA拓扑结构的适应性，增强了监控数据的准确性和可靠性。修复后的代码更加健壮，能够正确处理各种边界情况和异常场景。
