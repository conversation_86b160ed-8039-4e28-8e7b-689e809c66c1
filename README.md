# Kunpeng Topology Affinity Plugin(TAP)

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](LICENSE)
[![Go Version](https://img.shields.io/badge/Go-1.23+-blue.svg)](go.mod)
[![Kubernetes](https://img.shields.io/badge/Kubernetes-1.23+-blue.svg)](go.mod)

Topology Affinity Plugin (TAP) 是一个高性能的 Kubernetes 节点级资源调度插件，专为优化容器资源分配和提升应用性能而设计。它通过智能的拓扑感知策略，实现 CPU、内存和 GPU 资源的最优分配，显著提升应用在 NUMA 架构下的性能表现。

## 🚀 核心特性

### 🔧 多策略支持
- **Topology-Aware Policy**: 基于硬件拓扑的智能资源分配策略
- **NUMA-Aware Policy**: 针对 NUMA 架构优化的资源分配策略

### 🏗️ 硬件拓扑感知
- 自动发现 CPU Socket、Die、NUMA 节点等硬件拓扑结构
- 支持多 Socket、多 Die 复杂硬件架构
- 智能识别 GPU 设备分布和 NUMA 亲和性

### 🐳 多容器运行时支持
- **Docker**: 完整的 Docker 运行时支持
- **Containerd**: 原生 CRI 接口支持
- 透明代理，无需修改现有容器配置

### 📊 资源优化
- CPU 亲和性优化，减少跨 NUMA 访问
- 内存拓扑感知，优化内存访问延迟
- GPU 设备亲和性，提升 GPU 计算效率
- 支持 QoS 类别的差异化处理

### 🔄 高可用性
- 优雅重启和故障恢复
- 实时监控和指标收集

## 📋 系统要求

推荐使用如下配置：
- **操作系统**: OpenEuler 20.03, 22.03
- **Go 版本**: 1.23+
- **Kubernetes**: 1.23+
- **容器运行时**: Docker 或 Containerd
- **硬件**: Kunpeng 920系列处理器

## 🛠️ 快速开始

### 1. 构建项目

克隆项目：

```bash
git clone https://github.com/your-org/topo-affinity-plugin.git
```

进入项目目录：

```bash
cd topo-affinity-plugin
```

构建二进制文件：

```bash
make build
```

### 2. 安装服务

#### Docker 运行时

安装服务：

```bash
make install-service-docker
```

启动服务：

```bash
make start-service
```

检查服务状态：

```bash
systemctl status kunpeng-tap
```

#### Containerd 运行时

安装服务：

```bash
make install-service-containerd
```

启动服务：

```bash
make start-service
```

检查服务状态：

```bash
systemctl status kunpeng-tap
```

### 3. 配置 Kubelet

#### Docker 运行时

默认kubelet使用 docker-shim 与docker连接，修改 Kubelet 配置，添加以下参数：

```bash
--docker-endpoint=unix:///var/run/kunpeng/tap-runtime-proxy.sock
```

重启 Kubelet：

```bash
systemctl daemon-reload
```

```bash
systemctl restart kubelet
```

#### Containerd 运行时

修改 Kubelet 配置，添加以下参数：

```bash
--container-runtime=remote --container-runtime-endpoint=unix:///var/run/kunpeng/tap-runtime-proxy.sock
```

重启 Kubelet：

```bash
systemctl daemon-reload
```

```bash
systemctl restart kubelet
```

## ⚙️ 配置选项

### 策略选择

通过 `--resource-policy` 参数选择资源分配策略：

使用拓扑感知策略：

```bash
--resource-policy=topology-aware
```

使用 NUMA 感知策略：

```bash
--resource-policy=numa-aware
```

### 内存拓扑感知

启用内存拓扑感知功能：

```bash
--enable-memory-topology=true
```

### 监控配置

配置监控端点：

```bash
--metrics-bind-address=:9091
```

## 📈 性能优化

### Topology-Aware Policy

该策略通过以下方式优化资源分配：

1. **硬件拓扑发现**: 自动识别 CPU Socket、Die、NUMA 节点层级结构
2. **智能评分算法**: 基于资源容量、亲和性、共置容器数量等多维度评分
3. **GPU 亲和性**: 自动识别 GPU 设备分布，优化 GPU 计算任务
4. **内存拓扑**: 支持内存亲和性设置，减少内存访问延迟

### NUMA-Aware Policy

该策略专注于 NUMA 架构优化：

1. **NUMA 节点选择**: 基于 CPU 使用率选择最优 NUMA 节点
2. **Cpuset分配**: 为容器分配NUMA节点中连续的 CPU 核心
3. **QoS 支持**: 针对不同 QoS 类别采用差异化策略

## 🔍 监控和调试

### 指标监控

插件提供丰富的 Prometheus 指标：

- 容器创建/删除统计
- 策略执行时间

### 日志级别

通过参数选项调整日志级别：

```bash
--v=5
```

### 状态检查

检查服务状态：

```bash
systemctl status kunpeng-tap
```

查看服务日志：

```bash
journalctl -u kunpeng-tap -f
```

## 🧪 测试

### 单元测试

```bash
make test
```

## 🤝 贡献指南

我们欢迎社区贡献！请查看以下指南：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 Apache 2.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

- **文档**: [项目 Wiki](https://github.com/your-org/topo-affinity-plugin/wiki)
- **问题反馈**: [Gitee Issues](https://gitee.com/kunpeng_compute/topo-affinity-plugin/issues)、[Kunpeng社区](https://www.hikunpeng.com/document/detail/zh/kunpengcpfs/basicAccelFeatures/comAccel/kunpengkp_tap_04_002.html)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！
