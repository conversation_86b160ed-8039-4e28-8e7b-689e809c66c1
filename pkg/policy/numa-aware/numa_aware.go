/*
 * Copyright (c) 2025 Huawei Technology corp.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package numa_aware

import (
	"math"
	"sort"
	"strconv"

	v1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
	"kunpeng.huawei.com/tap/pkg/cache"
	"kunpeng.huawei.com/tap/pkg/monitoring"
	policy "kunpeng.huawei.com/tap/pkg/policy"
	"kunpeng.huawei.com/tap/pkg/sysfs"
)

const (
	// PolicyName is the name used to identify this policy
	PolicyName = "numa-aware"

	// PolicyDescription describes the policy functionality
	PolicyDescription = "Policy for NUMA-aware container resource allocation"
)

type NumaAwarePolicy struct {
	policy.BasePolicy
	cache cache.Cache
}

// NewNumaAwarePolicy creates a new NUMA-aware policy
func NewNumaAwarePolicy(cache cache.Cache) policy.Policy {
	return &NumaAwarePolicy{
		BasePolicy: *policy.NewBasePolicy(PolicyName, PolicyDescription),
		cache:      cache,
	}
}

// Name returns the policy name
func (p *NumaAwarePolicy) Name() string {
	return PolicyName
}

// Description returns the policy description
func (p *NumaAwarePolicy) Description() string {
	return PolicyDescription
}

// SetCache sets the shared cache for the policy
func (p *NumaAwarePolicy) SetCache(c cache.Cache) {
	p.cache = c
}

// PreCreateContainerHook implements NUMA-aware CPU set allocation
func (p *NumaAwarePolicy) PreCreateContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
	containerCtx, ok := ctx.(*policy.ContainerContext)
	if !ok {
		klog.ErrorS(nil, "Invalid context type for PreCreateContainerHook")
		return nil, nil
	}

	request := containerCtx.Request

	resourceReq, resourceLimit := request.Resources.GetRequests(), request.Resources.GetLimits()
	if resourceReq == nil || resourceLimit == nil {
		klog.V(0).InfoS("Resource requirements or limits are nil")
		return nil, nil
	}

	qos := policy.ParseCgroupForQOSClass(request.CgroupParent)

	var alloc *policy.Allocation

	switch qos {
	case v1.PodQOSGuaranteed, v1.PodQOSBurstable:
		// Modify cpuset range for Guaranteed and Burstable pods
		alloc = p.allocateCPUSet(resourceReq, resourceLimit)
	case v1.PodQOSBestEffort:
		// Don't modify anything for BestEffort pods
	}

	// 如果成功分配了资源，更新指标
	if alloc != nil {
		// 延迟更新指标，确保容器已经被添加到cache中
		go func() {
			p.updateAllNumaMetrics()
		}()
	}

	return alloc, nil
}

func (p *NumaAwarePolicy) allocateCPUSet(request, limit *v1.ResourceList) *policy.Allocation {
	alloc := policy.NewAllocation()

	if request == nil || limit == nil {
		klog.V(0).InfoS("Request or limit is nil")
		return nil
	}

	reqCpu, limitCpu := request.Cpu().AsApproximateFloat64(), limit.Cpu().AsApproximateFloat64()
	klog.V(5).InfoS("reqCpu and limitCpu", "reqCpu", reqCpu, "limitCpu", limitCpu)
	nodeResources := p.cache.GetNodeResources()

	klog.V(5).InfoS("nodeResources", "resources", nodeResources)

	// 按照请求资源选择节点
	// Fix: 默认 CPU 中所有 NUMA 的cpu数量一致
	cpuTotalInNode := nodeResources[0].CpuTotal

	// 超出一个节点，暂不处理
	if reqCpu > cpuTotalInNode || limitCpu > cpuTotalInNode {
		return nil
	}
	// 选择出 CPU 已分配量最低的节点
	var preferedNode int = -1
	used := math.MaxFloat64

	for i, v := range nodeResources {
		if v.CpuUsed < used {
			preferedNode = i
			used = v.CpuUsed
		}
	}

	klog.V(5).InfoS("Selected preferred node", "nodeId", preferedNode)
	// TODO: 设置方法修改从系统信息直接获取，check 超线程等情况
	alloc.SetCPUSetCpus(strconv.Itoa(preferedNode*int(cpuTotalInNode)) + "-" + strconv.Itoa((preferedNode+1)*int(cpuTotalInNode)-1))

	klog.V(5).InfoS("Allocated cpuset", "cpuset", alloc.Resources.CpusetCpus)

	return alloc
}

// updateNumaNodeContainerMetrics 更新NUMA节点容器数量指标
func (p *NumaAwarePolicy) updateNumaNodeContainerMetrics() {
	if p.cache == nil {
		return
	}

	// 获取CPU信息 - 通过GetNodeResources间接获取
	nodeResources := p.cache.GetNodeResources()
	if len(nodeResources) == 0 {
		klog.V(3).InfoS("No NUMA node resources available for metrics update")
		return
	}

	// 获取本地CPU信息用于解析cpuset
	cpuInfo, err := sysfs.GetLocalCPUInfo()
	if err != nil {
		klog.ErrorS(err, "Failed to get local CPU info for metrics update")
		return
	}

	// 统计每个NUMA节点的容器数量
	numaNodeContainerCount := make(map[int32]int)

	// 遍历所有容器
	containers := p.cache.GetContainers()
	for _, container := range containers {
		// 获取容器的cpuset
		cpusetCpus := container.GetCpusetCpus()
		if cpusetCpus == "" {
			continue
		}

		// 根据cpuset确定NUMA节点
		numaNodeID := sysfs.GetNumaNodeFromCpuSet(cpuInfo, cpusetCpus)
		if numaNodeID >= 0 {
			numaNodeContainerCount[numaNodeID]++
		}
	}

	// 重置所有NUMA节点的指标
	monitoring.NumaNodeContainerCount.Reset()

	// 更新每个NUMA节点的容器数量指标
	for nodeID, count := range numaNodeContainerCount {
		monitoring.NumaNodeContainerCount.WithLabelValues(
			strconv.Itoa(int(nodeID)),
			"running",
		).Set(float64(count))

		klog.V(5).InfoS("Updated NUMA node container count metric",
			"numaNodeID", nodeID,
			"containerCount", count)
	}

	// 确保所有NUMA节点都有指标（即使容器数量为0）
	for i := range nodeResources {
		if _, exists := numaNodeContainerCount[int32(i)]; !exists {
			monitoring.NumaNodeContainerCount.WithLabelValues(
				strconv.Itoa(i),
				"running",
			).Set(0)
		}
	}
}

// updateNumaNodeCpuResourceMetrics 更新NUMA节点CPU资源使用量指标
func (p *NumaAwarePolicy) updateNumaNodeCpuResourceMetrics() {
	if p.cache == nil {
		return
	}

	// 获取NUMA节点资源信息
	nodeResources := p.cache.GetNodeResources()
	if len(nodeResources) == 0 {
		klog.V(3).InfoS("No NUMA node resources available for CPU metrics update")
		return
	}

	// 获取实际的NUMA节点ID列表（与nodeResources数组索引对应）
	cpuInfo, err := sysfs.GetLocalCPUInfo()
	if err != nil {
		klog.ErrorS(err, "Failed to get local CPU info for metrics update")
		return
	}

	// 获取排序后的NUMA节点ID列表
	var nodeIDs []int32
	for nodeID := range cpuInfo.TotalInfo.NodeToCPU {
		nodeIDs = append(nodeIDs, nodeID)
	}
	sort.Slice(nodeIDs, func(i, j int) bool {
		return nodeIDs[i] < nodeIDs[j]
	})

	// 重置所有CPU资源指标
	monitoring.NumaNodeCpuUsage.Reset()
	monitoring.NumaNodeCpuFree.Reset()
	monitoring.NumaNodeCpuTotal.Reset()
	monitoring.NumaNodeCpuUtilization.Reset()

	// 更新每个NUMA节点的CPU资源指标
	for i, nodeResource := range nodeResources {
		// 使用实际的NUMA节点ID作为标签
		var nodeIDStr string
		if i < len(nodeIDs) {
			nodeIDStr = strconv.Itoa(int(nodeIDs[i]))
		} else {
			// 如果索引超出范围，使用索引作为标签
			nodeIDStr = strconv.Itoa(i)
			klog.V(3).InfoS("NUMA node index out of range, using index as ID", "index", i)
		}

		// CPU使用量（核心数）
		monitoring.NumaNodeCpuUsage.WithLabelValues(nodeIDStr).Set(nodeResource.CpuUsed)

		// CPU空余量（核心数）
		monitoring.NumaNodeCpuFree.WithLabelValues(nodeIDStr).Set(nodeResource.CpuFree)

		// CPU总量（核心数）
		monitoring.NumaNodeCpuTotal.WithLabelValues(nodeIDStr).Set(nodeResource.CpuTotal)

		// CPU使用率（百分比）
		var utilization float64
		if nodeResource.CpuTotal > 0 {
			utilization = (nodeResource.CpuUsed / nodeResource.CpuTotal) * 100
		}
		monitoring.NumaNodeCpuUtilization.WithLabelValues(nodeIDStr).Set(utilization)

		klog.V(5).InfoS("Updated NUMA node CPU resource metrics",
			"numaNodeID", nodeIDStr,
			"arrayIndex", i,
			"cpuUsed", nodeResource.CpuUsed,
			"cpuFree", nodeResource.CpuFree,
			"cpuTotal", nodeResource.CpuTotal,
			"utilization", utilization)
	}
}

// updateAllNumaMetrics 更新所有NUMA相关指标
func (p *NumaAwarePolicy) updateAllNumaMetrics() {
	// 更新容器数量指标
	p.updateNumaNodeContainerMetrics()
	// 更新CPU资源指标
	p.updateNumaNodeCpuResourceMetrics()
}

// PostStartContainerHook 在容器启动后更新指标
func (p *NumaAwarePolicy) PostStartContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
	containerCtx, ok := ctx.(*policy.ContainerContext)
	if !ok {
		klog.ErrorS(nil, "Invalid context type for PostStartContainerHook")
		return nil, nil
	}

	klog.V(5).InfoS("Container started, updating NUMA metrics",
		"container", containerCtx.Request.ContainerMeta.Name)

	// 更新所有NUMA指标
	p.updateAllNumaMetrics()
	return nil, nil
}

// PostStopContainerHook 在容器停止后更新指标
func (p *NumaAwarePolicy) PostStopContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
	containerCtx, ok := ctx.(*policy.ContainerContext)
	if !ok {
		klog.ErrorS(nil, "Invalid context type for PostStopContainerHook")
		return nil, nil
	}

	klog.V(5).InfoS("Container stopped, updating NUMA metrics",
		"container", containerCtx.Request.ContainerMeta.ID)

	// 更新所有NUMA指标
	p.updateAllNumaMetrics()
	return nil, nil
}
