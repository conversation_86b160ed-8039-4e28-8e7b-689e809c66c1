# NUMA-aware 策略监控指标示例配置
# 本文件展示了如何使用 numa-aware 策略提供的 Prometheus 指标

# ========== NUMA-aware 策略监控指标 ==========

# 1. NUMA 节点容器数量指标
# tap_numa_node_container_count{numa_node_id="0",container_state="running"} 5
# tap_numa_node_container_count{numa_node_id="1",container_state="running"} 3

# 2. NUMA 节点 CPU 使用量指标（核心数）
# tap_numa_node_cpu_usage_cores{numa_node_id="0"} 2.5
# tap_numa_node_cpu_usage_cores{numa_node_id="1"} 1.8

# 3. NUMA 节点 CPU 空余量指标（核心数）
# tap_numa_node_cpu_free_cores{numa_node_id="0"} 1.5
# tap_numa_node_cpu_free_cores{numa_node_id="1"} 2.2

# 4. NUMA 节点 CPU 总量指标（核心数）
# tap_numa_node_cpu_total_cores{numa_node_id="0"} 4.0
# tap_numa_node_cpu_total_cores{numa_node_id="1"} 4.0

# 5. NUMA 节点 CPU 使用率指标（百分比）
# tap_numa_node_cpu_utilization_percent{numa_node_id="0"} 62.5
# tap_numa_node_cpu_utilization_percent{numa_node_id="1"} 45.0

# ========== 常用查询示例 ==========

# 1. 查看每个 NUMA 节点的容器数量
# tap_numa_node_container_count

# 2. 查看特定 NUMA 节点的容器数量
# tap_numa_node_container_count{numa_node_id="0"}

# 3. 查看所有 NUMA 节点的总容器数量
# sum(tap_numa_node_container_count)

# 4. 查看容器分布最不均匀的情况（最大值与最小值的差）
# max(tap_numa_node_container_count) - min(tap_numa_node_container_count)

# 5. 查看每个 NUMA 节点的容器数量占比
# tap_numa_node_container_count / sum(tap_numa_node_container_count) * 100

# 6. 查看每个 NUMA 节点的 CPU 使用情况
# tap_numa_node_cpu_usage_cores
# tap_numa_node_cpu_free_cores
# tap_numa_node_cpu_total_cores

# 7. 查看 NUMA 节点 CPU 使用率
# tap_numa_node_cpu_utilization_percent

# 8. 查看 CPU 使用率最高的 NUMA 节点
# max(tap_numa_node_cpu_utilization_percent)

# 9. 查看 CPU 负载不均衡情况
# max(tap_numa_node_cpu_utilization_percent) - min(tap_numa_node_cpu_utilization_percent)

# 10. 查看特定 NUMA 节点的 CPU 空余量
# tap_numa_node_cpu_free_cores{numa_node_id="0"}

# 11. 查看所有 NUMA 节点的总 CPU 使用量
# sum(tap_numa_node_cpu_usage_cores)

# 12. 查看平均 CPU 使用率
# avg(tap_numa_node_cpu_utilization_percent)

# ========== Grafana 面板配置示例 ==========

# 面板 1: NUMA 节点容器数量柱状图
# 查询: tap_numa_node_container_count
# 图表类型: Bar Chart
# 标题: "NUMA Node Container Distribution"

# 面板 2: NUMA 节点容器数量时间序列
# 查询: tap_numa_node_container_count
# 图表类型: Time Series
# 标题: "NUMA Node Container Count Over Time"

# 面板 3: 容器分布均匀性指标
# 查询: max(tap_numa_node_container_count) - min(tap_numa_node_container_count)
# 图表类型: Stat
# 标题: "Container Distribution Imbalance"

# ========== 告警规则示例 ==========

# 告警 1: NUMA 节点容器分布不均
# - alert: NumaContainerImbalance
#   expr: max(tap_numa_node_container_count) - min(tap_numa_node_container_count) > 10
#   for: 5m
#   labels:
#     severity: warning
#   annotations:
#     summary: "NUMA container distribution is imbalanced"
#     description: "The difference between max and min container count across NUMA nodes is {{ $value }}"

# 告警 2: 单个 NUMA 节点容器过多
# - alert: NumaNodeOverloaded
#   expr: tap_numa_node_container_count > 50
#   for: 2m
#   labels:
#     severity: critical
#   annotations:
#     summary: "NUMA node {{ $labels.numa_node_id }} has too many containers"
#     description: "NUMA node {{ $labels.numa_node_id }} has {{ $value }} containers, which exceeds the threshold"

# ========== 指标标签说明 ==========

# numa_node_id: NUMA 节点 ID (如 "0", "1", "2")
# container_state: 容器状态 (当前只支持 "running")

# ========== 使用说明 ==========

# 1. 确保启用了 numa-aware 策略
# 2. 配置 Prometheus 抓取 /metrics 端点
# 3. 指标会在容器创建、启动、停止时自动更新
# 4. 指标反映的是当前时刻各 NUMA 节点上运行的容器数量

# ========== 注意事项 ==========

# 1. 只有通过 numa-aware 策略分配的容器才会被统计
# 2. BestEffort QoS 类别的容器不会被分配到特定 NUMA 节点，因此不会被统计
# 3. 指标更新可能有轻微延迟，特别是在容器创建时
# 4. 如果容器的 cpuset 为空或无法解析，该容器不会被统计到任何 NUMA 节点
