# Topology-aware 策略监控指标查询示例
# 这些查询可以帮助监控容器在资源树中的分布情况和资源使用情况
# 基于实际的 metrics 定义更新

# ========== 1. 容器分布监控 ==========

# 查看每个节点上的容器数量（按节点名称）
# tap_topology_node_container_count{node_name="NUMA node #0", node_type="numa", hierarchy_level="numa", container_state="running"}

# 查看所有 NUMA 节点的容器分布
# sum by (node_name) (tap_topology_node_container_count{hierarchy_level="numa"})

# 查看每个 Socket 的容器分布
# sum by (node_name) (tap_topology_node_container_count{hierarchy_level="socket"})

# 查看不同层次级别的容器分布
# sum by (hierarchy_level) (tap_topology_node_container_count)

# 查看特定节点类型的容器分布
# tap_topology_node_container_count{node_type="numa", container_state="running"}

# ========== 2. 节点资源容量监控 ==========

# 查看每个节点的 CPU 总容量（核数）
# tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"}

# 查看每个 NUMA 节点的 CPU 容量
# tap_topology_node_capacity{hierarchy_level="numa", resource_type="cpu", capacity_type="total_cores"}

# 查看每个节点的内存总容量（KB）
# tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"}

# 查看所有 Socket 的 CPU 容量汇总
# sum by (node_name) (tap_topology_node_capacity{hierarchy_level="socket", resource_type="cpu", capacity_type="total_cores"})

# ========== 3. 节点资源使用监控 ==========

# 查看每个节点的 CPU 使用情况（已分配的毫核）
# tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}

# 查看每个 NUMA 节点的 CPU 请求量
# tap_topology_node_usage{hierarchy_level="numa", resource_type="cpu", usage_type="request_millicores"}

# 查看每个 NUMA 节点的 CPU 限制量
# tap_topology_node_usage{hierarchy_level="numa", resource_type="cpu", usage_type="limit_millicores"}

# 查看每个节点的内存使用情况（已分配的 KB）
# tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"}

# 计算 CPU 使用率（已分配 / 总容量）
# (tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"} / 1000) / tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"} * 100

# 计算内存使用率（已分配 / 总容量）
# tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"} / tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"} * 100

# ========== 4. 层次结构分析 ==========

# 查看不同层次级别的资源使用情况
# sum by (hierarchy_level, resource_type, usage_type) (tap_topology_node_usage)

# 查看 Socket 级别的资源使用汇总
# sum by (node_name) (tap_topology_node_usage{hierarchy_level="socket"})

# 查看 NUMA 级别的资源使用汇总
# sum by (node_name) (tap_topology_node_usage{hierarchy_level="numa"})

# ========== 5. 代理和钩子性能监控 ==========

# 查看代理请求持续时间
# histogram_quantile(0.95, sum by (request) (rate(tap_proxy_request_duration_seconds_bucket[5m])))

# 查看钩子请求持续时间
# histogram_quantile(0.95, sum by (hook) (rate(tap_hook_request_duration_seconds_bucket[5m])))

# 查看代理健康状态
# tap_proxy_up

# ========== 6. 告警规则示例 ==========
groups:
- name: topology_aware_alerts
  rules:
  # CPU 使用率过高告警
  - alert: HighCPUUsageOnNode
    expr: (tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"} / 1000) / tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"} > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on node {{ $labels.node_name }}"
      description: "CPU usage is above 80% on {{ $labels.hierarchy_level }} node {{ $labels.node_name }}"

  # 内存使用率过高告警
  - alert: HighMemoryUsageOnNode
    expr: tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"} / tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"} > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage on node {{ $labels.node_name }}"
      description: "Memory usage is above 80% on {{ $labels.hierarchy_level }} node {{ $labels.node_name }}"

  # 容器分布不均衡告警
  - alert: ContainerDistributionImbalance
    expr: max by (hierarchy_level) (tap_topology_node_container_count{hierarchy_level="numa"}) - min by (hierarchy_level) (tap_topology_node_container_count{hierarchy_level="numa"}) > 5
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Container distribution imbalance detected"
      description: "Container distribution is imbalanced across NUMA nodes (difference > 5 containers)"

  # 代理服务不健康告警
  - alert: ProxyDown
    expr: tap_proxy_up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Kunpeng-TAP proxy is down"
      description: "The Kunpeng-TAP proxy service is not responding"

  # 代理请求延迟过高告警
  - alert: HighProxyLatency
    expr: histogram_quantile(0.95, sum by (request) (rate(tap_proxy_request_duration_seconds_bucket[5m]))) > 1.0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High proxy request latency"
      description: "95th percentile proxy request latency is above 1 second for {{ $labels.request }}"

  # 钩子请求延迟过高告警
  - alert: HighHookLatency
    expr: histogram_quantile(0.95, sum by (hook) (rate(tap_hook_request_duration_seconds_bucket[5m]))) > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High hook request latency"
      description: "95th percentile hook request latency is above 0.5 seconds for {{ $labels.hook }}"

# ========== 7. Grafana 仪表板查询示例 ==========

# 容器分布饼图（按 NUMA 节点）
# sum by (node_name) (tap_topology_node_container_count{hierarchy_level="numa", container_state="running"})

# 容器分布饼图（按 Socket）
# sum by (node_name) (tap_topology_node_container_count{hierarchy_level="socket", container_state="running"})

# CPU 使用率趋势图（按节点）
# (tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"} / 1000) / tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"} * 100

# 内存使用率趋势图（按节点）
# tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"} / tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"} * 100

# CPU 资源使用量趋势图（毫核）
# tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}

# 内存资源使用量趋势图（KB）
# tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"}

# 层次结构资源容量对比图
# sum by (hierarchy_level, resource_type) (tap_topology_node_capacity)

# 层次结构资源使用对比图
# sum by (hierarchy_level, resource_type, usage_type) (tap_topology_node_usage)

# 代理请求延迟分布图
# histogram_quantile(0.50, sum by (request) (rate(tap_proxy_request_duration_seconds_bucket[5m])))
# histogram_quantile(0.95, sum by (request) (rate(tap_proxy_request_duration_seconds_bucket[5m])))
# histogram_quantile(0.99, sum by (request) (rate(tap_proxy_request_duration_seconds_bucket[5m])))

# 钩子请求延迟分布图
# histogram_quantile(0.50, sum by (hook) (rate(tap_hook_request_duration_seconds_bucket[5m])))
# histogram_quantile(0.95, sum by (hook) (rate(tap_hook_request_duration_seconds_bucket[5m])))
# histogram_quantile(0.99, sum by (hook) (rate(tap_hook_request_duration_seconds_bucket[5m])))

# ========== 8. 实际 Metrics 标签说明 ==========

# tap_topology_node_capacity 标签:
# - node_name: 节点名称 (如 "NUMA node #0", "socket #0", "root")
# - node_type: 节点类型 (如 "numa", "socket", "virtual")
# - node_id: 节点ID (如 "0", "1", "2")
# - hierarchy_level: 层次级别 (如 "numa", "socket", "virtual")
# - resource_type: 资源类型 (如 "cpu", "memory")
# - capacity_type: 容量类型 (如 "total_cores", "total_kb")

# tap_topology_node_usage 标签:
# - node_name: 节点名称
# - node_type: 节点类型
# - node_id: 节点ID
# - hierarchy_level: 层次级别
# - resource_type: 资源类型 (如 "cpu", "memory")
# - usage_type: 使用类型 (如 "allocated_millicores", "request_millicores", "limit_millicores", "allocated_kb")

# tap_topology_node_container_count 标签:
# - node_name: 节点名称
# - node_type: 节点类型
# - node_id: 节点ID
# - hierarchy_level: 层次级别
# - container_state: 容器状态 (如 "running")

# ========== 9. 使用示例 ==========

# 查看系统整体资源使用情况:
# sum(tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}) / 1000  # 总 CPU 使用（核）
# sum(tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"}) / 1024 / 1024  # 总内存使用（GB）

# 查看最繁忙的节点:
# topk(5, tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"})
# topk(5, tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"})

# 查看容器分布最多的节点:
# topk(5, tap_topology_node_container_count{container_state="running"})